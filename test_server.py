"""
Test server pro simulaci sale requestu s callbackem
"""

import time
import threading
import json
import requests
from bottle import Bottle, request, response

# Inicializace bottle aplikace
app = Bottle()

def send_callback(payment_data):
    """
    Odešle callback po 20s čekání
    """
    time.sleep(5)  # Čekání 20 sekund
    
    callback_url = "http://localhost:8000/transaction/callback"  # Hardcoded callback URL
    
    # Příprava callback dat
    callback_data = {
        "status": "success",
        "msg": "Payment successful",
        "type": "payment",
        "t": "",
        "auth_code": "123456"  # Simulov<PERSON>ý auth kód
    }
    
    try:
        # Odeslání callbacku
        response = requests.post(
            callback_url,
            json=callback_data,
            headers={'Content-Type': 'application/json'},
            timeout=5
        )
        print(f"Callback odeslán, status code: {response.status_code}")
        print(f"Callback response: {response.text}")
        
    except Exception as e:
        print(f"Chyba při odesí<PERSON>ání callbacku: {e}")

@app.post('/')
def handle_sale():
    """
    Endpoint pro zpracování sale requestu
    """
    try:
        data = request.json
        print(f"Přijat sale request: {json.dumps(data, indent=2)}")
        
        # Kontrola typu requestu
        if not data or 'type' not in data or data['type'] != 'sale':
            response.status = 400
            return {
                "status": "failed",
                "msg": "Invalid request type"
            }
            
        # Kontrola částky
        if 'amount' not in data:
            response.status = 400
            return {
                "status": "failed",
                "msg": "Missing amount"
            }
            
        # Spuštění asynchronního callbacku
        callback_thread = threading.Thread(
            target=send_callback,
            args=(data,),
            daemon=True
        )
        callback_thread.start()
        
        # Okamžitá odpověď na request
        return {
            "status": "success",
            "msg": "accepted"
        }
        
    except Exception as e:
        print(f"Chyba při zpracování requestu: {e}")
        response.status = 500
        return {
            "status": "failed",
            "msg": "Internal server error"
        }

if __name__ == '__main__':
    print("Spouštím testovací server na http://localhost:8080")
    print("Pro test použijte:")
    print('curl -X POST http://localhost:8080/ -H "Content-Type: application/json" -d \'{"type": "sale", "amount": 100}\'')
    app.run(host='localhost', port=8080) 