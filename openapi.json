{"openapi": "3.1.0", "info": {"title": "FastAPI", "version": "0.1.0"}, "paths": {"/storage/categories": {"get": {"tags": ["storage"], "summary": "List Categories", "description": "List all available storage categories and their prices.", "operationId": "list_categories_storage_categories_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListCategoriesResponse"}}}}}, "security": [{"BearerAuth": []}]}}, "/storage/reserve": {"post": {"tags": ["storage"], "summary": "Create Reservation", "description": "Create a new storage reservation.\nThis endpoint initiates the reservation process. The user selects a box size,\nprovides an email, and the system reserves a box and returns a session ID\nfor the payment process.", "operationId": "create_reservation_storage_reserve_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateReservationRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateReservationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"BearerAuth": []}]}}, "/storage/pickup": {"post": {"tags": ["storage"], "summary": "Pickup", "description": "Handle the pickup of a stored item.\nThe user provides a reservation PIN. If the pickup is within the allowed time,\nthe box opens. If it's late, the endpoint returns a surcharge message.", "operationId": "pickup_storage_pickup_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PickupRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PickupResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"BearerAuth": []}]}}, "/storage/pay-surcharge": {"post": {"tags": ["storage"], "summary": "Pay Surcharge", "description": "Initiate the payment process for a surcharge.\nIf a user is late for pickup, they must pay a surcharge. This endpoint\ncreates a session for the surcharge payment.", "operationId": "pay_surcharge_storage_pay_surcharge_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SurchargeRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SurchargeResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"BearerAuth": []}]}}, "/product/insert-custom": {"post": {"tags": ["product"], "summary": "Insert Custom Product", "description": "Create a custom product.\n\nRequired Parameters:\n- section_id: ID of the section where product will be placed\n- price: Product price", "operationId": "insert_custom_product_product_insert_custom_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsertCustomRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Insert Custom Product Product Insert Custom Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"BearerAuth": []}]}}, "/product/insert": {"post": {"tags": ["product"], "summary": "Insert Product", "description": "Insert a product with EAN code into a section.\nCreates database record, gets hardware configuration, and creates hardware session for WebSocket.\n\nRequired Parameters:\n- section_id: ID of the section where product will be placed\n- ean: EAN code of the product", "operationId": "insert_product_product_insert_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductInsertRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductInsertResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"BearerAuth": []}]}}, "/product/remove": {"delete": {"tags": ["product"], "summary": "Remove Product", "description": "Set status to 0 for all products in a specific section.\n\nRequired Parameters:\n- section_id: ID of the section to remove products from", "operationId": "remove_product_product_remove_delete", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemoveRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Remove Product Product Remove Delete"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"BearerAuth": []}]}}, "/product/remove-all": {"delete": {"tags": ["product"], "summary": "Remove All Products", "description": "Set status to 0 for all products in all sections.", "operationId": "remove_all_products_product_remove_all_delete", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Remove All Products Product Remove All Delete"}}}}}, "security": [{"BearerAuth": []}]}}, "/product/list": {"get": {"tags": ["product"], "summary": "List Products", "description": "List all active products across all sections (status = 1).", "operationId": "list_products_product_list_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response List Products Product List Get"}}}}}, "security": [{"BearerAuth": []}]}}, "/product/list-section": {"get": {"tags": ["product"], "summary": "List Section Products", "description": "List all active products in a specific section (status = 1).\n\nRequired Parameters:\n- section: ID of the section to list products from", "operationId": "list_section_products_product_list_section_get", "security": [{"BearerAuth": []}], "parameters": [{"name": "section", "in": "query", "required": true, "schema": {"type": "integer", "description": "Section ID to list products from", "title": "Section"}, "description": "Section ID to list products from"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response List Section Products Product List Section Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/product/reserve-section": {"post": {"tags": ["product"], "summary": "Reserve Section", "description": "Reserve a section for exclusive use.\nOnly works if there is a product with status=1 in the section.\nSets reserved=1 and saves reservation_pin for the product.\nThe PIN must be unique across all active products.\n\nRequired Parameters:\n- section_id: ID of the section to reserve (integer)\n\nOptional Parameters:\n- reservation_pin: Custom PIN for the reservation. If not provided, a random 6-digit PIN will be generated.\n  If provided, must be unique across active products.", "operationId": "reserve_section_product_reserve_section_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReserveSectionRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Reserve Section Product Reserve Section Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"BearerAuth": []}]}}, "/product/purchase": {"post": {"tags": ["product"], "summary": "Purchase Product", "description": "Purchase a product from a section using reservation PIN.\nProduct must be reserved (reserved=1) and PIN must match.\n\nRequired Parameters:\n- section_id: ID of the section with the product\n- reservation_pin: PIN that was assigned during reservation", "operationId": "purchase_product_product_purchase_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PurchaseRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Purchase Product Product Purchase Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"BearerAuth": []}]}}, "/product/pickup": {"post": {"tags": ["product"], "summary": "Pickup Product", "description": "Initialize product pickup process\nCreates transaction session and starts flow", "operationId": "pickup_product_product_pickup_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductPickupRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"BearerAuth": []}]}}, "/product/test-external-api/{ean}": {"get": {"tags": ["product"], "summary": "Test External Api", "description": "Test endpoint for external API integration.\nReturns product information for given EAN without saving to database.", "operationId": "test_external_api_product_test_external_api__ean__get", "security": [{"BearerAuth": []}], "parameters": [{"name": "ean", "in": "path", "required": true, "schema": {"type": "string", "title": "<PERSON><PERSON>"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Test External Api Product Test External Api  Ean  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/transaction/callback": {"post": {"tags": ["transaction"], "summary": "Transaction Callback", "description": "Handle callback from payment terminal", "operationId": "transaction_callback_transaction_callback_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentCallback"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransactionCallback"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/operator/session": {"post": {"summary": "Create Operator Session", "description": "Vytvoří operátorskou session pro FSM sekvence s volbou čekání na zavření dvířek.", "operationId": "create_operator_session_operator_session_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OperatorSessionRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/box/sections": {"get": {"tags": ["box"], "summary": "Get Box Sections", "operationId": "get_box_sections_box_sections_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/box/layout": {"get": {"tags": ["box"], "summary": "Get Box Layout", "operationId": "get_box_layout_box_layout_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/box/check-pin": {"post": {"tags": ["box"], "summary": "<PERSON>", "operationId": "check_pin_box_check_pin_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PinCheckRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"CreateReservationRequest": {"properties": {"size_category": {"type": "integer", "title": "Size Category", "description": "Size category of the box"}, "email": {"type": "string", "title": "Email", "description": "Customer's email address"}}, "type": "object", "required": ["size_category", "email"], "title": "CreateReservationRequest", "description": "Request model for creating a storage reservation."}, "CreateReservationResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "message": {"type": "string", "title": "Message"}, "reservation": {"anyOf": [{"$ref": "#/components/schemas/StorageReservation"}, {"type": "null"}]}, "session_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Id"}, "websocket_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Websocket Url"}}, "type": "object", "required": ["success", "message"], "title": "CreateReservationResponse", "description": "Response model for creating a storage reservation."}, "ExternalProduct": {"properties": {"uuid": {"type": "string", "title": "<PERSON><PERSON>"}, "price": {"type": "number", "title": "Price"}, "ean": {"type": "string", "title": "<PERSON><PERSON>"}, "text": {"$ref": "#/components/schemas/ExternalProductTextData"}, "ageControl": {"type": "integer", "title": "Agecontrol"}, "status": {"type": "integer", "title": "Status"}, "createdAt": {"type": "string", "title": "Createdat"}, "maxDays": {"type": "integer", "title": "Maxdays"}, "coverImage": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Coverimage"}}, "type": "object", "required": ["uuid", "price", "ean", "text", "ageControl", "status", "createdAt", "maxDays"], "title": "ExternalProduct", "description": "External API product model"}, "ExternalProductText": {"properties": {"name": {"type": "string", "title": "Name", "default": ""}, "description": {"type": "string", "title": "Description", "default": ""}}, "type": "object", "title": "ExternalProductText", "description": "Text information for product in multiple languages"}, "ExternalProductTextData": {"properties": {"en": {"anyOf": [{"$ref": "#/components/schemas/ExternalProductText"}, {"type": "null"}]}, "cs": {"$ref": "#/components/schemas/ExternalProductText"}}, "type": "object", "required": ["cs"], "title": "ExternalProductTextData", "description": "Language-specific text data"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "HardwareConfigResponse": {"properties": {"section_id": {"type": "integer", "title": "Section Id"}, "lock_id": {"type": "integer", "title": "Lock Id"}, "is_tempered": {"type": "boolean", "title": "Is Tempered"}, "led_section": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Led Section"}}, "type": "object", "required": ["section_id", "lock_id", "is_tempered"], "title": "HardwareConfigResponse", "description": "Hardware configuration for a section"}, "InsertCustomRequest": {"properties": {"price": {"anyOf": [{"type": "number"}, {"type": "string"}], "title": "Price"}, "section_id": {"type": "integer", "title": "Section Id"}}, "type": "object", "required": ["price", "section_id"], "title": "InsertCustomRequest"}, "ListCategoriesResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "categories": {"items": {"$ref": "#/components/schemas/StorageCategory"}, "type": "array", "title": "Categories"}}, "type": "object", "required": ["success", "categories"], "title": "ListCategoriesResponse", "description": "Response model for listing storage categories."}, "OperatorSessionRequest": {"properties": {"close": {"type": "boolean", "title": "Close", "default": false}}, "type": "object", "title": "OperatorSessionRequest"}, "PaymentCallback": {"properties": {"status": {"type": "string", "title": "Status", "description": "Status platby ('success' nebo 'fail')"}, "msg": {"type": "string", "title": "Msg", "description": "Zpráva o výsledku platby"}, "type": {"type": "string", "title": "Type", "description": "<PERSON><PERSON> callbacku", "default": "payment"}, "t": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "T", "description": "Timestamp z terminálu"}, "auth_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Auth Code", "description": "Autorizační kód platby"}, "variable_symbol": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Variable Symbol", "description": "Variable symbol sent with payment request for session identification"}}, "type": "object", "required": ["status", "msg"], "title": "PaymentCallback", "description": "Model pro callback z platebního termin<PERSON>"}, "PickupRequest": {"properties": {"reservation_pin": {"type": "string", "title": "Reservation Pin", "description": "Reservation PIN for pickup"}}, "type": "object", "required": ["reservation_pin"], "title": "PickupRequest", "description": "Request model for picking up a stored item."}, "PickupResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "message": {"type": "string", "title": "Message"}, "surcharge": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Surcharge"}, "session_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Id"}, "websocket_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Websocket Url"}}, "type": "object", "required": ["success", "message"], "title": "PickupResponse", "description": "Response model for picking up a stored item."}, "PinCheckRequest": {"properties": {"pin": {"type": "string", "title": "<PERSON>n"}}, "type": "object", "required": ["pin"], "title": "PinCheckRequest"}, "ProductInsertRequest": {"properties": {"section_id": {"type": "integer", "title": "Section Id"}, "ean": {"type": "string", "title": "<PERSON><PERSON>"}}, "type": "object", "required": ["section_id", "ean"], "title": "ProductInsertRequest", "description": "Request model for product insertion"}, "ProductInsertResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "message": {"type": "string", "title": "Message"}, "product": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Product"}, "external_product": {"anyOf": [{"$ref": "#/components/schemas/ExternalProduct"}, {"type": "null"}]}, "hardware_session_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Hardware Session Id"}, "hardware_config": {"anyOf": [{"$ref": "#/components/schemas/HardwareConfigResponse"}, {"type": "null"}]}, "websocket_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Websocket Url"}, "error_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error Code"}}, "type": "object", "required": ["success", "message"], "title": "ProductInsertResponse", "description": "Response model for product insertion with hardware session"}, "ProductPickupRequest": {"properties": {"section_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Section Id"}, "reservation_pin": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reservation Pin"}}, "type": "object", "title": "ProductPickupRequest", "description": "Request model for product pickup"}, "PurchaseRequest": {"properties": {"section_id": {"type": "integer", "title": "Section Id"}, "reservation_pin": {"type": "string", "title": "Reservation Pin"}}, "type": "object", "required": ["section_id", "reservation_pin"], "title": "PurchaseRequest"}, "RemoveRequest": {"properties": {"section_id": {"type": "integer", "title": "Section Id"}}, "type": "object", "required": ["section_id"], "title": "RemoveRequest"}, "ReserveSectionRequest": {"properties": {"section_id": {"type": "integer", "title": "Section Id"}, "reservation_pin": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reservation Pin"}}, "type": "object", "required": ["section_id"], "title": "ReserveSectionRequest"}, "StorageCategory": {"properties": {"id": {"type": "integer", "title": "Id"}, "size_category": {"type": "integer", "title": "Size Category"}, "price": {"type": "integer", "title": "Price"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["id", "size_category", "price", "created_at", "updated_at"], "title": "StorageCategory", "description": "Represents a storage category."}, "StorageReservation": {"properties": {"id": {"type": "integer", "title": "Id"}, "uuid": {"type": "string", "title": "<PERSON><PERSON>"}, "box_uuid": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Box Uuid"}, "section_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Section Id"}, "status": {"type": "integer", "title": "Status"}, "reservation_pin": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reservation Pin"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email"}, "price": {"type": "number", "title": "Price"}, "max_days": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Max Days"}, "paid_status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Paid Status"}, "category": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Category"}, "last_update": {"type": "string", "format": "date-time", "title": "Last Update"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["id", "uuid", "status", "price", "last_update", "created_at"], "title": "StorageReservation", "description": "Represents a storage reservation."}, "SurchargeRequest": {"properties": {"reservation_pin": {"type": "string", "title": "Reservation Pin", "description": "Reservation PIN for surcharge payment"}}, "type": "object", "required": ["reservation_pin"], "title": "SurchargeRequest", "description": "Request model for paying a surcharge."}, "SurchargeResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "message": {"type": "string", "title": "Message"}, "session_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Id"}, "websocket_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Websocket Url"}}, "type": "object", "required": ["success", "message"], "title": "SurchargeResponse", "description": "Response model for paying a surcharge."}, "TransactionCallback": {"properties": {"transaction_id": {"type": "string", "title": "Transaction Id", "description": "ID transakce"}, "status": {"type": "string", "title": "Status", "description": "Status operace"}, "type": {"type": "string", "title": "Type", "description": "Typ operace"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp", "description": "<PERSON><PERSON> call<PERSON>"}, "message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message", "description": "Zpráva o výsledku"}, "data": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Data", "description": "Dodatečná data"}, "error": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error", "description": "Chybová zpráva"}}, "type": "object", "required": ["transaction_id", "status", "type"], "title": "TransactionCallback", "description": "Unified model pro všechny typy callbacků"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}, "securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer"}}}}