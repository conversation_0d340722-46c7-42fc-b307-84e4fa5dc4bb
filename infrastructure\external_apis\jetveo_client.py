"""
Jetveo API Client - External API integration for product data.
Handles communication with Jetveo API for product information.
"""

import logging
import aiohttp
import asyncio
from typing import Optional, Dict, Any
from os import getenv
from dotenv import load_dotenv

from sale.models import ExternalProduct, ExternalProductResponse

load_dotenv()

logger = logging.getLogger(__name__)

class JetveoClient:
    """Client for Jetveo external API"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.base_url = getenv("EXTERNAL_API_BASE_URL", "https://4a8fa4e1-8948-4b4c-9baa-4839d158ad96.eu.jetveo.io")
        self.partner_code = getenv("EXTERNAL_API_PARTNER_CODE", "drmax")
        self.api_token = getenv("EXTERNAL_API_TOKEN", "Gj1lojJdMIrgC13psFsWwveas8PYLdUC")
        self.timeout = int(getenv("EXTERNAL_API_TIMEOUT", "10"))
    
    async def fetch_product_by_ean(self, ean: str) -> Optional[ExternalProduct]:
        """
        Fetch product information from external API by EAN code.
        
        Args:
            ean: EAN code to search for
            
        Returns:
            ExternalProduct object if found, None otherwise
        """
        try:
            url = f"{self.base_url}/api/product"
            params = {
                "offset": 0,
                "limit": 100,  # Search in larger batch to increase chance of finding EAN
                "partner-code": self.partner_code
            }
            
            headers = {
                "jv-api-key": self.api_token
            }
            
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url, params=params, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        # Parse response using Pydantic model
                        external_response = ExternalProductResponse(**data)
                        
                        if external_response.success:
                            # Search for product with matching EAN
                            for product in external_response.items:
                                if product.ean == ean:
                                    self.logger.info(f"Found product for EAN {ean}: {product.text.cs.name}")
                                    return product
                            
                            self.logger.warning(f"Product with EAN {ean} not found in external API")
                            return None
                        else:
                            self.logger.error(f"External API returned unsuccessful response: {external_response.message}")
                            return None
                    else:
                        self.logger.error(f"External API request failed with status {response.status}")
                        return None
                        
        except asyncio.TimeoutError:
            self.logger.error(f"External API request timed out for EAN {ean}")
            return None
        except Exception as e:
            self.logger.error(f"Error fetching product from external API: {e}")
            return None
    
    async def search_products(self, query: str, limit: int = 100) -> Optional[ExternalProductResponse]:
        """
        Search products in external API.
        
        Args:
            query: Search query
            limit: Maximum number of results
            
        Returns:
            ExternalProductResponse object or None if failed
        """
        try:
            url = f"{self.base_url}/api/product"
            params = {
                "offset": 0,
                "limit": limit,
                "partner-code": self.partner_code,
                "q": query
            }
            
            headers = {
                "jv-api-key": self.api_token
            }
            
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url, params=params, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return ExternalProductResponse(**data)
                    else:
                        self.logger.error(f"External API search failed with status {response.status}")
                        return None
                        
        except asyncio.TimeoutError:
            self.logger.error(f"External API search timed out for query: {query}")
            return None
        except Exception as e:
            self.logger.error(f"Error searching products in external API: {e}")
            return None
    
    async def get_product_categories(self) -> Optional[Dict[str, Any]]:
        """
        Get product categories from external API.
        
        Returns:
            Categories data or None if failed
        """
        try:
            url = f"{self.base_url}/api/categories"
            params = {
                "partner-code": self.partner_code
            }
            
            headers = {
                "jv-api-key": self.api_token
            }
            
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url, params=params, headers=headers) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        self.logger.error(f"External API categories request failed with status {response.status}")
                        return None
                        
        except asyncio.TimeoutError:
            self.logger.error("External API categories request timed out")
            return None
        except Exception as e:
            self.logger.error(f"Error fetching categories from external API: {e}")
            return None
    
    async def test_connection(self) -> bool:
        """
        Test connection to external API.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            url = f"{self.base_url}/api/product"
            params = {
                "offset": 0,
                "limit": 1,
                "partner-code": self.partner_code
            }
            
            headers = {
                "jv-api-key": self.api_token
            }
            
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url, params=params, headers=headers) as response:
                    return response.status == 200
                        
        except Exception as e:
            self.logger.error(f"External API connection test failed: {e}")
            return False

# Global client instance
jetveo_client = JetveoClient() 