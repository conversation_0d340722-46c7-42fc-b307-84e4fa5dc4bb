from fastapi import APIRouter, HTTPException, Body, Query, Depends
from typing import List, Optional, Dict
from decimal import Decimal
from uuid import uuid4
from pydantic import BaseModel
import mysql.connector
from datetime import datetime
from os import getenv
from dotenv import load_dotenv
from auth.auth_bearer import BearerAuth
import random
import aiohttp
import asyncio
import logging

# Import hardware components
from managers import session_manager, SessionType, SessionStatus
from managers.session_manager import SectionConfig

# Import sale models
from .models import (
    ProductInsertRequest, ProductInsertResponse, HardwareConfigResponse,
    ProductPickupRequest, ProductPickupResponse, FlowPickupResponse,
    ExternalProduct, ExternalProductResponse
)

load_dotenv()

logger = logging.getLogger(__name__)









auth_scheme = BearerAuth()

class RemoveRequest(BaseModel):
    section_id: int

class ReserveSectionRequest(BaseModel):
    section_id: int  # Přijímáme jako int pro API
    reservation_pin: Optional[str] = None

class InsertCustomRequest(BaseModel):
    price: Decimal
    section_id: int

class PurchaseRequest(BaseModel):
    section_id: int
    reservation_pin: str

router = APIRouter(
    prefix="/product",
    tags=["product"],
    dependencies=[Depends(auth_scheme)]  # Aplikuje autentizaci na všechny endpointy v routeru
)

@router.post("/insert-custom")
async def insert_custom_product(request: InsertCustomRequest) -> Dict:
    """
    Create a custom product.
    
    Required Parameters:
    - section_id: ID of the section where product will be placed
    - price: Product price
    """
    try:
        # Import product service
        from domains.product.service import product_service
        
        # Use product service for insert logic
        result = await product_service.insert_custom_product(
            section_id=request.section_id,
            price=request.price
        )
        
        if not result["success"]:
            raise HTTPException(
                status_code=200,
                detail=result
            )
        
        return {
            "status": "success",
            "message": result["message"],
            "product": result["product"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in insert-custom endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.post("/insert", response_model=ProductInsertResponse)
async def insert_product(request: ProductInsertRequest) -> ProductInsertResponse:
    """
    Insert a product with EAN code into a section.
    Creates database record, gets hardware configuration, and creates hardware session for WebSocket.
    
    Required Parameters:
    - section_id: ID of the section where product will be placed
    - ean: EAN code of the product
    """
    try:
        # Import product service
        from domains.product.service import product_service
        
        # Use product service for insert logic
        result = await product_service.insert_product_with_ean(
            section_id=request.section_id,
            ean=request.ean
        )
        
        if not result["success"]:
            return ProductInsertResponse(
                success=False,
                message=result["message"],
                error_code=result["error_code"]
            )
        
        # Get hardware configuration for this section
        hardware_config = await product_service.get_hardware_config_for_section(request.section_id)
        if not hardware_config:
            return ProductInsertResponse(
                success=False,
                message=f"No hardware configuration found for section {request.section_id}",
                error_code="110"
            )
        
        # Create hardware session for WebSocket connection
        hardware_session = session_manager.create_session(
            session_type=SessionType.BACKEND_SALE,
            sections=[hardware_config],
            status=SessionStatus.PENDING,
            transaction_data={
                "product_uuid": result["product"]["uuid"],
                "section_id": request.section_id,
                "ean": request.ean,
                "operation": "insert"
            }
        )
        
        return ProductInsertResponse(
            success=True,
            message=result["message"],
            product=result["product"],
            external_product=result["external_product"],
            hardware_session_id=hardware_session.session_id,
            hardware_config=HardwareConfigResponse(
                section_id=hardware_config.section_id,
                lock_id=hardware_config.lock_id,
                is_tempered=hardware_config.is_tempered,
                led_section=hardware_config.led_section
            ),
            websocket_url=f"/ws/{hardware_session.session_id}"
        )
        
    except Exception as e:
        logger.error(f"Error in insert endpoint: {e}")
        return ProductInsertResponse(
            success=False,
            message=f"Internal server error: {str(e)}",
            error_code="112"
        )

@router.delete("/remove")
async def remove_product(request: RemoveRequest) -> Dict:
    """
    Set status to 0 for all products in a specific section.
    
    Required Parameters:
    - section_id: ID of the section to remove products from
    """
    try:
        # Import product service
        from domains.product.service import product_service
        
        # Use product service for remove logic
        result = await product_service.remove_product(request.section_id)
        
        if not result["success"]:
            raise HTTPException(
                status_code=500,
                detail=result
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in remove endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.delete("/remove-all")
async def remove_all_products() -> Dict:
    """
    Set status to 0 for all products in all sections.
    """
    try:
        # Import product service
        from domains.product.service import product_service
        
        # Use product service for remove all logic
        result = await product_service.remove_all_products()
        
        if not result["success"]:
            raise HTTPException(
                status_code=500,
                detail=result
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in remove-all endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/list")
async def list_products() -> Dict:
    """
    List all active products across all sections (status = 1).
    """
    try:
        # Import product service
        from domains.product.service import product_service
        
        # Use product service for list logic
        result = await product_service.list_products()
        
        return result
        
    except Exception as e:
        logger.error(f"Error in list endpoint: {e}")
        return {
            "success": False,
            "error_code": "103",
            "message": f"Database error: {str(e)}",
            "products": []
        }

@router.get("/list-section")
async def list_section_products(
    section: int = Query(..., description="Section ID to list products from")
) -> Dict:
    """
    List all active products in a specific section (status = 1).
    
    Required Parameters:
    - section: ID of the section to list products from
    """
    try:
        # Import product service
        from domains.product.service import product_service
        
        # Use product service for list section logic
        result = await product_service.list_section_products(section)
        
        # Add section_id to result
        result["section_id"] = section
        
        return result
        
    except Exception as e:
        logger.error(f"Error in list-section endpoint: {e}")
        return {
            "success": False,
            "error_code": "103",
            "message": f"Database error: {str(e)}",
            "section_id": section,
            "products": []
        }


@router.post("/reserve-section")
async def reserve_section(
    request: ReserveSectionRequest
) -> Dict:
    """
    Reserve a section for exclusive use.
    Only works if there is a product with status=1 in the section.
    Sets reserved=1 and saves reservation_pin for the product.
    The PIN must be unique across all active products.
    
    Required Parameters:
    - section_id: ID of the section to reserve (integer)
    
    Optional Parameters:
    - reservation_pin: Custom PIN for the reservation. If not provided, a random 6-digit PIN will be generated.
      If provided, must be unique across active products.
    """
    try:
        # Import product service
        from domains.product.service import product_service
        
        # Use product service for reserve logic
        result = await product_service.reserve_section(
            section_id=request.section_id,
            reservation_pin=request.reservation_pin
        )
        
        if not result["success"]:
            raise HTTPException(
                status_code=200,
                detail=result
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in reserve-section endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}") 

@router.post("/purchase")
async def purchase_product(request: PurchaseRequest) -> Dict:
    """
    Purchase a product from a section using reservation PIN.
    Product must be reserved (reserved=1) and PIN must match.
    
    Required Parameters:
    - section_id: ID of the section with the product
    - reservation_pin: PIN that was assigned during reservation
    """
    try:
        # Import product service
        from domains.product.service import product_service
        
        # Use product service for purchase logic
        result = await product_service.purchase_product(
            section_id=request.section_id,
            reservation_pin=request.reservation_pin
        )
        
        if not result["success"]:
            raise HTTPException(
                status_code=400,
                detail=result
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in purchase endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}") 

@router.post("/pickup")
async def pickup_product(request: ProductPickupRequest):
    """
    Initialize product pickup process
    Creates transaction session and starts flow
    """
    logger.info(f"Product pickup requested - section_id: {request.section_id}, reservation_pin: {request.reservation_pin}")
    
    try:
        # Import product service
        from domains.product.service import product_service
        
        # Use product service for pickup logic
        result = await product_service.pickup_product(
            section_id=request.section_id,
            reservation_pin=request.reservation_pin
        )
        
        return FlowPickupResponse(**result)
            
    except ValueError as e:
        # No product found
        raise HTTPException(status_code=404, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in pickup endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/test-external-api/{ean}")
async def test_external_api(ean: str) -> Dict:
    """
    Test endpoint for external API integration.
    Returns product information for given EAN without saving to database.
    """
    from infrastructure.external_apis import jetveo_client
    
    external_product = await jetveo_client.fetch_product_by_ean(ean)
    
    if external_product:
        return {
            "success": True,
            "message": f"Product found for EAN {ean}",
            "product": external_product.dict()
        }
    else:
        return {
            "success": False,
            "message": f"Product with EAN {ean} not found in external API",
            "error_code": "111"
        } 