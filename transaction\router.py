from fastapi import APIRouter, HTTPException
from typing import Optional
import logging
from datetime import datetime

from .models import TransactionCallback, PaymentCallback, TransactionType
from .manager import transaction_manager
from managers import session_manager, SessionType

router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/callback", response_model=TransactionCallback)
async def transaction_callback(callback: PaymentCallback) -> TransactionCallback:
    """
    Handle callback from payment terminal
    """
    logger.info(f"Received payment callback: {callback}")
    
    try:
        # Find active payment session (either TRANSACTION or PRODUCT_FLOW)
        transaction_sessions = session_manager.get_sessions_by_type(SessionType.TRANSACTION)
        product_flow_sessions = session_manager.get_sessions_by_type(SessionType.PRODUCT_FLOW)
        
        logger.info(f"Looking for active payment session - Transaction sessions: {len(transaction_sessions)}, Product flow sessions: {len(product_flow_sessions)}")
        
        active_session = None
        
        # Check transaction sessions first (legacy)
        for session in transaction_sessions:
            if session.transaction_data.get("transaction_type") == TransactionType.PAYMENT:
                active_session = session
                break
        
        # If not found, check product flow sessions
        if not active_session:
            # First try to find session by variable_symbol (session_id) if available
            if callback.variable_symbol:
                for session in product_flow_sessions:
                    if session.session_id == callback.variable_symbol:
                        active_session = session
                        logger.info(f"Found product flow session by variable_symbol (session_id): {session.session_id}")
                        break
            
            # If not found by variable_symbol, try to find the most recent session with active WebSocket connection
            if not active_session and len(product_flow_sessions) > 0:
                from managers.ws_manager import ws_manager
                # Find all sessions with active WebSocket connections
                active_websocket_sessions = [
                    session for session in product_flow_sessions 
                    if ws_manager.is_connected(session.session_id)
                ]
                
                if active_websocket_sessions:
                    # Sort by creation time (most recent first) and take the newest one
                    active_websocket_sessions.sort(key=lambda s: s.created_at, reverse=True)
                    active_session = active_websocket_sessions[0]
                    logger.info(f"Found most recent active product flow session with WebSocket: {active_session.session_id}")
                    if len(active_websocket_sessions) > 1:
                        logger.warning(f"Multiple active WebSocket sessions found ({len(active_websocket_sessions)}), using most recent")
                
                # If no session with active WebSocket found, take the most recent one
                if not active_session:
                    # Sort by creation time (most recent first)
                    product_flow_sessions.sort(key=lambda s: s.created_at, reverse=True)
                    active_session = product_flow_sessions[0]
                    logger.info(f"Using most recent product flow session: {active_session.session_id}")
                    logger.warning(f"No active WebSocket found for session {active_session.session_id}, this may indicate an issue")
        
        if not active_session:
            raise HTTPException(
                status_code=404,
                detail="No active payment session found"
            )
        
        # Check if this is a product flow session
        if active_session.session_type == SessionType.PRODUCT_FLOW:
            logger.info(f"Processing payment callback for product flow session: {active_session.session_id}")
            
            # Handle payment callback for product flow
            from domains.product.websocket_handler import handle_payment_callback
            
            result = await handle_payment_callback(
                session_id=active_session.session_id,
                status=callback.status,
                message=callback.msg
            )
            
            if not result:
                raise HTTPException(
                    status_code=500,
                    detail="Failed to process product flow payment callback"
                )
                
            # Return a proper TransactionCallback for product flow
            return TransactionCallback(
                transaction_id=active_session.session_id,  # Use session_id as transaction_id
                status=callback.status,
                type=callback.type,
                timestamp=datetime.now(),
                message="Payment callback processed for product flow",
                data={
                    "session_type": "product_flow",
                    "auth_code": callback.auth_code,
                    "terminal_timestamp": callback.t
                } if callback.auth_code or callback.t else {"session_type": "product_flow"}
            )
        
        else:
            # Legacy transaction handling
            transaction_callback = TransactionCallback(
                transaction_id=active_session.transaction_data.get("transaction_id"),
                status=callback.status,
                type=callback.type,
                timestamp=datetime.now(),
                message=callback.msg,
                data={
                    "auth_code": callback.auth_code,
                    "terminal_timestamp": callback.t
                } if callback.auth_code or callback.t else None
            )
            
            logger.info(f"Processing transaction callback: {transaction_callback}")
            
            # Process callback
            success = await transaction_manager.handle_external_callback(transaction_callback)
            
            if not success:
                raise HTTPException(
                    status_code=500,
                    detail="Failed to process transaction callback"
                )
            
        return transaction_callback
            
    except HTTPException:
        raise
    except Exception as err:
        logger.error(f"Error processing transaction callback: {err}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal error: {str(err)}"
        )