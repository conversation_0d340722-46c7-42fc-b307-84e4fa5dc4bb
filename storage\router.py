from fastapi import APIRouter, Depends, HTTPException
from auth.auth_bearer import BearerAuth
from .models import (
    CreateReservationRequest, CreateReservationResponse,
    PickupRequest, PickupResponse,
    SurchargeRequest, SurchargeResponse,
    ListCategoriesResponse
)
from domains.storage.service import storage_service

router = APIRouter(
    prefix="/storage",
    tags=["storage"],
    dependencies=[Depends(BearerAuth())]
)

@router.get("/categories", response_model=ListCategoriesResponse)
async def list_categories():
    """
    List all available storage categories and their prices.
    """
    result = await storage_service.list_categories()
    return result

@router.post("/reserve", response_model=CreateReservationResponse)
async def create_reservation(request: CreateReservationRequest):
    """
    Create a new storage reservation.
    This endpoint initiates the reservation process. The user selects a box size,
    provides an email, and the system reserves a box and returns a session ID
    for the payment process.
    """
    result = await storage_service.create_reservation(request.size_category, request.email)
    if not result["success"]:
        raise HTTPException(status_code=400, detail=result["message"])
    return result

@router.post("/pickup", response_model=PickupResponse)
async def pickup(request: PickupRequest):
    """
    Handle the pickup of a stored item.
    The user provides a reservation PIN. If the pickup is within the allowed time,
    the box opens. If it's late, the endpoint returns a surcharge message.
    """
    result = await storage_service.pickup(request.reservation_pin)
    if not result["success"] and "surcharge" in result:
        raise HTTPException(status_code=402, detail={"message": result["message"], "surcharge": result["surcharge"]})
    if not result["success"]:
        raise HTTPException(status_code=400, detail=result["message"])
    return result

@router.post("/pay-surcharge", response_model=SurchargeResponse)
async def pay_surcharge(request: SurchargeRequest):
    """
    Initiate the payment process for a surcharge.
    If a user is late for pickup, they must pay a surcharge. This endpoint
    creates a session for the surcharge payment.
    """
    result = await storage_service.pay_surcharge(request.reservation_pin)
    if not result["success"]:
        raise HTTPException(status_code=400, detail=result["message"])
    return result
