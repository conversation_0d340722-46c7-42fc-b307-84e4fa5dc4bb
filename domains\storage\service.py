import logging
from typing import Dict, Optional, Any, List
from uuid import uuid4
from infrastructure.repositories.storage_repository import storage_repository
from managers.session_manager import session_manager, SessionType, SessionStatus

logger = logging.getLogger(__name__)

class StorageService:
    def __init__(self):
        self.logger = logging.getLogger(__name__)

    async def list_categories(self) -> Dict[str, Any]:
        """
        List all storage categories.
        """
        categories = await storage_repository.get_storage_categories()
        return {
            "success": True,
            "categories": categories
        }

    async def create_reservation(self, size_category: int, email: str) -> Dict[str, Any]:
        """
        Create a new storage reservation.
        """
        categories = await storage_repository.get_storage_categories()
        category_info = next((c for c in categories if c['size_category'] == size_category), None)

        if not category_info:
            return {"success": False, "message": "Invalid size category"}

        price = category_info['price']
        box = await storage_repository.find_available_box(size_category)
        if not box:
            return {"success": False, "message": "No available boxes for this category"}

        reservation = await storage_repository.create_reservation(email, size_category, price, box)
        if not reservation:
            return {"success": False, "message": "Failed to create reservation"}

        session_id = str(uuid4())
        session = session_manager.create_session(
            session_id=session_id,
            session_type=SessionType.STORAGE_FLOW,
            transaction_data={
                "reservation_uuid": reservation["uuid"],
                "operation": "create_reservation",
                "amount": price
            }
        )

        return {
            "success": True,
            "message": "Reservation created successfully. Please proceed to payment.",
            "reservation": reservation,
            "session_id": session_id,
            "websocket_url": f"/ws/{session_id}"
        }

    async def pickup(self, reservation_pin: str) -> Dict[str, Any]:
        """
        Handle pickup of a stored item.
        """
        reservation = await storage_repository.find_reservation_by_pin(reservation_pin)
        if not reservation:
            return {"success": False, "message": "Invalid reservation PIN"}

        surcharge = await storage_repository.calculate_surcharge(reservation)
        if surcharge > 0:
            return {
                "success": False,
                "message": f"Surcharge of {surcharge} is due. Please pay the surcharge to proceed.",
                "surcharge": surcharge
            }

        # Open the box
        session_id = str(uuid4())
        session_manager.create_session(
            session_id=session_id,
            session_type=SessionType.STORAGE_FLOW,
            transaction_data={
                "reservation_uuid": reservation["uuid"],
                "operation": "pickup",
            }
        )
        
        # In a real scenario, you would interact with the hardware to open the box.
        # Here we just update the reservation status.
        await storage_repository.update_reservation_status(reservation["id"], 0, "completed")

        return {
            "success": True,
            "message": "Box is open. You can now retrieve your item.",
            "session_id": session_id,
            "websocket_url": f"/ws/{session_id}"
        }

    async def pay_surcharge(self, reservation_pin: str) -> Dict[str, Any]:
        """
        Handle payment of a surcharge.
        """
        reservation = await storage_repository.find_reservation_by_pin(reservation_pin)
        if not reservation:
            return {"success": False, "message": "Invalid reservation PIN"}

        surcharge = await storage_repository.calculate_surcharge(reservation)
        if surcharge == 0:
            return {"success": False, "message": "No surcharge is due for this reservation."}

        session_id = str(uuid4())
        session_manager.create_session(
            session_id=session_id,
            session_type=SessionType.STORAGE_FLOW,
            transaction_data={
                "reservation_uuid": reservation["uuid"],
                "operation": "pay_surcharge",
                "amount": surcharge
            }
        )

        return {
            "success": True,
            "message": "Please proceed to payment for the surcharge.",
            "session_id": session_id,
            "websocket_url": f"/ws/{session_id}"
        }

storage_service = StorageService()
