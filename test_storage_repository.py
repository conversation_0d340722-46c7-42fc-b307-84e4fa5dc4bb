import asyncio
import logging
from dotenv import load_dotenv

# Load environment variables from .env file FIRST
load_dotenv()

# Now import the repository, which depends on the loaded env vars
from infrastructure.repositories.storage_repository import storage_repository

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def run_tests():
    """
    Runs a series of tests on the StorageRepository to verify database operations.
    """
    logger.info("--- Starting StorageRepository Tests ---")

    # Log the database configuration being used to aid debugging
    logger.info(f"Repository is configured for host: {storage_repository.db_config.get('host')}:{storage_repository.db_config.get('port')}")

    # 1. Test: Get Storage Categories
    logger.info("[1/5] Testing: get_storage_categories()")
    try:
        categories = await storage_repository.get_storage_categories()
        if categories:
            logger.info(f"SUCCESS: Found {len(categories)} storage categories.")
            # print(categories) # Uncomment to see details
        else:
            logger.warning("WARNING: No storage categories found. This might be expected if the table is empty.")
    except Exception as e:
        logger.error(f"FAILED: get_storage_categories() raised an exception: {e}", exc_info=True)
        return

    # 2. Test: Create a new reservation
    # logger.info("\n[2/5] Testing: create_reservation()")
    # new_reservation = None
    # try:
    #     # We assume a size_category of 1 exists with a price of 10.0
    #     # and a box is available.
    #     size_category = 1
    #     price = 10.0
    #     email = "<EMAIL>"
    #     box = {"box_uuid": "test_box_001", "section_id": "A1"}
        
    #     new_reservation = await storage_repository.create_reservation(email, size_category, price, box)
    #     if new_reservation and new_reservation.get("id"):
    #         logger.info(f"SUCCESS: Created new reservation with ID: {new_reservation['id']} and PIN: {new_reservation['reservation_pin']}")
    #         # print(new_reservation) # Uncomment to see details
    #     else:
    #         logger.error("FAILED: create_reservation() did not return a valid reservation object.")
    #         return
    # except Exception as e:
    #     logger.error(f"FAILED: create_reservation() raised an exception: {e}", exc_info=True)
    #     return

    # # 3. Test: Find the reservation by PIN
    # logger.info("\n[3/5] Testing: find_reservation_by_pin()")
    # try:
    #     reservation_pin = new_reservation["reservation_pin"]
    #     found_reservation = await storage_repository.find_reservation_by_pin(reservation_pin)
    #     if found_reservation and found_reservation["id"] == new_reservation["id"]:
    #         logger.info(f"SUCCESS: Found reservation with ID {found_reservation['id']} using PIN {reservation_pin}.")
    #     else:
    #         logger.error(f"FAILED: Could not find reservation using PIN {reservation_pin}.")
    #         return
    # except Exception as e:
    #     logger.error(f"FAILED: find_reservation_by_pin() raised an exception: {e}", exc_info=True)
    #     return

    # # 4. Test: Update reservation status
    # logger.info("\n[4/5] Testing: update_reservation_status()")
    # try:
    #     reservation_id = new_reservation["id"]
    #     updated = await storage_repository.update_reservation_status(reservation_id, 0, "completed")
    #     if updated:
    #         logger.info(f"SUCCESS: Updated status for reservation ID {reservation_id}.")
    #     else:
    #         logger.error(f"FAILED: Could not update status for reservation ID {reservation_id}.")
    # except Exception as e:
    #     logger.error(f"FAILED: update_reservation_status() raised an exception: {e}", exc_info=True)

    # # 5. Cleanup: Revert status for next test run
    # logger.info("\n[5/5] Cleaning up: Reverting reservation status for next run.")
    # try:
    #     reservation_id = new_reservation["id"]
    #     reverted = await storage_repository.update_reservation_status(reservation_id, 1, "unpaid")
    #     if reverted:
    #         logger.info(f"SUCCESS: Reverted status for reservation ID {reservation_id}.")
    #     else:
    #         logger.error(f"FAILED: Could not revert status for reservation ID {reservation_id}.")
    # except Exception as e:
    #     logger.error(f"FAILED: Cleanup step raised an exception: {e}", exc_info=True)

    # logger.info("\n--- StorageRepository Tests Finished ---")


if __name__ == "__main__":
    # Ensure you have a .env file with DB credentials in the same directory
    # or that environment variables are set.
    asyncio.run(run_tests())
