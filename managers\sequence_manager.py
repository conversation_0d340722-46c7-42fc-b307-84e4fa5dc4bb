"""
Sequence manager for orchestrating locker opening sequences.
Handles LED signaling, door state monitoring, and sequential locker operations.
"""

import asyncio
import logging
from typing import Dict, Optional, List
from hardware import <PERSON>r<PERSON><PERSON><PERSON><PERSON>, LEDColor
from .ws_manager import ws_manager
from .error_manager import error_manager, SectionError, ErrorType, ErrorCode
from .session_manager import session_manager, SessionType, SessionStatus, SectionConfig
from config import device_config

logger = logging.getLogger(__name__)

class SequenceManager:
    """Manager for handling locker sequences with LED signaling"""
    
    def __init__(self):
        self.locker_controller = LockerController()
        self.active_sequences: Dict[str, asyncio.Task] = {}
        self.door_timeouts = {
            "open": device_config.fsm_config["door_open_timeout"],
            "close": device_config.fsm_config["door_close_timeout"],
        }
        self.polling_interval = device_config.fsm_config["polling_interval"]
        
        # Track global LED state across all sequences
        self.led_states: Dict[int, Optional[str]] = {}  # {led_id: color or None}
        
    async def start_fsm_sequence(
        self, 
        session_id: str, 
        sections: List[SectionConfig],
        pin: str,
        wait_for_close: bool = True  # defaultně True pro všechny kromě operátora
    ) -> bool:
        """
        Start a complete FSM sequence
        
        Args:
            session_id: Session ID
            sections: List of sections to process
            pin: PIN for the sequence
            
        Returns:
            True if sequence started successfully
        """
        try:
            # Create or update session
            session = session_manager.get_session(session_id)
            if not session:
                session = session_manager.create_session(
                    session_type=SessionType.FSM_SEQUENCE,
                    pin=pin,
                    sections=sections,
                    status=SessionStatus.IN_PROGRESS
                )
                session_id = session.session_id
            else:
                session_manager.update_session(
                    session_id,
                    sections=sections,
                    pin=pin,
                    status=SessionStatus.IN_PROGRESS
                )
            
            # Start sequence task
            task = asyncio.create_task(
                self._execute_sequence(session_id, sections, wait_for_close=wait_for_close)
            )
            self.active_sequences[session_id] = task
            
            logger.info(f"Started FSM sequence {session_id} with {len(sections)} sections")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start FSM sequence: {e}")
            await error_manager.handle_error(
                SectionError(
                    f"Failed to start sequence: {e}",
                    ErrorType.SESSION,
                    ErrorCode.UNKNOWN,
                    details={"session_id": session_id}
                )
            )
            return False
    
    async def _update_led_state(self, led_id: int, color: Optional[str], session_id: str):
        """
        Update single LED while maintaining global LED state with retry logic
        
        Args:
            led_id: LED ID to update
            color: New color (None to turn off)
            session_id: Session ID for WebSocket updates
        """
        if not device_config.fsm_config["led_enabled"]:
            return
        
        # Update global LED state
        if color is None:
            self.led_states.pop(led_id, None)
        else:
            self.led_states[led_id] = color
        
        # Try to update LED hardware with 2 attempts (as requested)
        success = False
        max_attempts = 2
        
        for attempt in range(max_attempts):
            try:
                # Send complete LED state to hardware
                if self.led_states:
                    success = await self.locker_controller.set_multiple_led_colors(self.led_states)
                else:
                    # No LEDs should be on
                    success = await self.locker_controller.clear_all_leds()
                
                if success:
                    break  # Success, no need for retry
                    
                if attempt < max_attempts - 1:  # Not the last attempt
                    logger.warning(f"LED update attempt {attempt + 1} failed for LED {led_id}, retrying...")
                    await asyncio.sleep(1)  # Brief delay before retry
                    
            except Exception as e:
                logger.error(f"Exception during LED update attempt {attempt + 1} for LED {led_id}: {e}")
                if attempt < max_attempts - 1:
                    await asyncio.sleep(1)
        
        if success:
            await ws_manager.send(session_id, {
                "type": "led_update",
                "led_id": led_id,
                "color": color or "off",
                "global_led_state": dict(self.led_states),
                "message": f"LED {led_id} set to {color or 'off'}"
            })
        else:
            # LED failed but this is not critical - log warning and continue
            logger.warning(f"Failed to update LED {led_id} to {color} after {max_attempts} attempts, continuing without LED update")
            await ws_manager.send(session_id, {
                "type": "led_warning",
                "led_id": led_id,
                "color": color or "off",
                "message": f"Warning: LED {led_id} update failed, continuing sequence"
            })
    
    async def _execute_sequence(self, session_id: str, sections: List[SectionConfig], wait_for_close: bool = True):
        """Execute the full sequence of sections"""
        try:
            # Sort sections: tempered first, non-tempered last
            sorted_sections = sorted(sections, key=lambda s: (not s.is_tempered, s.section_id))
            
            # Find index of last tempered section for LED disable timing
            last_tempered_index = -1
            for idx, section in enumerate(sorted_sections):
                if section.is_tempered:
                    last_tempered_index = idx
            
            # Initialize LED system
            await self._initialize_sequence_leds(sorted_sections, session_id)
            
            success_count = 0
            
            for idx, section in enumerate(sorted_sections):
                try:
                    session_manager.update_session(session_id, current_section_index=idx)
                    
                    # Send status update via WebSocket
                    await ws_manager.send(session_id, {
                        "type": "section_update",
                        "section_id": section.section_id,
                        "status": "processing",
                        "message": f"Starting section process {idx + 1} of {len(sections)}"
                    })
                    
                    # Process individual section
                    session = session_manager.get_session(session_id)
                    if session and session.session_type == SessionType.OPERATOR_FSM:
                        success = await self._handle_operator_section_sequence(
                            session_id=session_id,
                            section=section,
                            total_sections=len(sorted_sections),
                            current_index=idx + 1,
                            wait_for_close=wait_for_close
                        )
                    else:
                        success = await self._handle_customer_section_sequence(
                            session_id=session_id,
                            section=section,
                            total_sections=len(sorted_sections),
                            current_index=idx + 1
                        )
                    
                    if success:
                        success_count += 1
                        await ws_manager.send(session_id, {
                            "type": "section_update",
                            "section_id": section.section_id,
                            "status": "completed",
                            "message": f"Section {section.section_id} completed successfully"
                        })
                        
                        # Disable LED system after last tempered section
                        if idx == last_tempered_index and last_tempered_index >= 0:
                            await self._disable_sequence_leds(session_id)
                            await ws_manager.send(session_id, {
                                "type": "system_update",
                                "message": "LED system disabled after last tempered section"
                            })
                        
                    else:
                        # Log failure but continue to next section instead of breaking
                        logger.warning(f"Section {section.section_id} failed, continuing to next section")
                        await ws_manager.send(session_id, {
                            "type": "section_update", 
                            "section_id": section.section_id,
                            "status": "failed",
                            "message": f"Section {section.section_id} failed, continuing to next section"
                        })
                        
                except Exception as e:
                    # Log error but continue to next section instead of breaking
                    logger.error(f"Error processing section {section.section_id}: {e}, continuing to next section")
                    await ws_manager.send(session_id, {
                        "type": "section_update",
                        "section_id": section.section_id,
                        "status": "error",
                        "message": f"Error: {str(e)}, continuing to next section"
                    })
            
            # Update final session status
            final_status = SessionStatus.COMPLETED if success_count == len(sorted_sections) else SessionStatus.FAILED
            session_manager.update_session(session_id, status=final_status)
            
            # Disable LED system only if not already disabled (no tempered sections)
            if last_tempered_index == -1:
                await self._disable_sequence_leds(session_id)
                await ws_manager.send(session_id, {
                    "type": "system_update",
                    "message": "LED system disabled (no tempered sections)"
                })
            
            # Send completion message
            await ws_manager.send(session_id, {
                "type": "sequence_complete",
                "success_count": success_count,
                "total_count": len(sorted_sections),
                "status": final_status
            })
            
        except Exception as e:
            logger.error(f"Sequence execution failed: {e}")
            session_manager.update_session(session_id, status=SessionStatus.FAILED)
            await ws_manager.send(session_id, {
                "type": "error",
                "message": f"Sequence failed: {str(e)}"
            })
        finally:
            # Clean up
            self.active_sequences.pop(session_id, None)
            # LED cleanup is handled in sequence logic, not needed here
    
    async def _handle_section_sequence(
        self,
        session_id: str,
        section: SectionConfig, 
        total_sections: int,
        current_index: int
    ) -> bool:
        """Handle processing of a single section"""
        
        # Check if this is an operator session
        session = session_manager.get_session(session_id)
        if session and session.session_type == SessionType.OPERATOR_FSM:
            return await self._handle_operator_section_sequence(
                session_id, section, total_sections, current_index
            )
        
        # Regular customer sequence
        return await self._handle_customer_section_sequence(
            session_id, section, total_sections, current_index
        )
    
    async def _handle_operator_section_sequence(
        self,
        session_id: str,
        section: SectionConfig, 
        total_sections: int,
        current_index: int,
        wait_for_close: bool = False
    ) -> bool:
        """Handle processing of a single section FOR OPERATOR - no door closing required"""
        
        try:
            section_id = section.section_id  # Visual ID for user interface
            lock_id = section.lock_id        # Hardware lock ID for commands
            is_tempered = section.is_tempered
            led_id = section.led_section
            
            # 1. Unlock the locker FIRST
            await ws_manager.send(session_id, {
                "type": "section_update",
                "section_id": section_id,
                "status": "unlocking",
                "message": f"Odemkávám schránku {section_id}"
            })
            
            unlock_success = await self.locker_controller.unlock_locker(lock_id, is_tempered)
            if not unlock_success:
                raise SectionError(
                    f"Failed to unlock locker {section_id}",
                    ErrorType.HARDWARE,
                    ErrorCode.UNLOCK_FAILED,
                    section_id=str(section_id)
                )
            
            # 2. ONLY if unlock was successful, set LED to green (current section)
            if led_id and device_config.fsm_config["led_enabled"]:
                await self._update_led_state(led_id, LEDColor.GREEN, session_id)
            
            # 3. Wait for door to be opened
            await ws_manager.send(session_id, {
                "type": "section_update",
                "section_id": section_id,
                "status": "waiting_open",
                "message": f"Čekám na otevření schránky {section_id}"
            })
            
            door_opened = await self._wait_for_door_state(
                section_id, lock_id, True, self.door_timeouts["open"], is_tempered, session_id
            )
            
            if not door_opened:
                raise SectionError(
                    f"Timeout waiting for door {section_id} to open",
                    ErrorType.TIMEOUT,
                    ErrorCode.DOOR_TIMEOUT,
                    section_id=str(section_id)
                )
            
            # 4. PRO OPERÁTORA: IHNED zamknout tempered zámky (aby se dveře daly zavřít)
            if is_tempered:
                await ws_manager.send(session_id, {
                    "type": "section_update",
                    "section_id": section_id,
                    "status": "locking_after_open",
                    "message": f"Schránka {section_id} otevřena, zámek zamčen pro zavření"
                })
                
                lock_success = await self.locker_controller.lock_locker(lock_id, is_tempered)
                if not lock_success:
                    raise SectionError(
                        f"Failed to lock locker {section_id} after opening",
                        ErrorType.HARDWARE,
                        ErrorCode.LOCK_FAILED,
                        section_id=str(section_id)
                    )
            
            # 5. PRO OPERÁTORA: čekat na zavření dveří pouze pokud wait_for_close==True
            if wait_for_close:
                await ws_manager.send(session_id, {
                    "type": "section_update",
                    "section_id": section_id,
                    "status": "waiting_close",
                    "message": f"Door {section_id} opened, waiting for close"
                })
                close_timeout = (device_config.fsm_config["door_close_timeout"] if is_tempered 
                                 else device_config.fsm_config["non_tempered_close_timeout"])
                door_closed = await self._wait_for_door_state(
                    section_id, lock_id, False, close_timeout, is_tempered, session_id
                )
                if not door_closed:
                    raise SectionError(
                        f"Timeout waiting for door {section_id} to close",
                        ErrorType.TIMEOUT,
                        ErrorCode.DOOR_TIMEOUT,
                        section_id=str(section_id)
                    )
                # Turn off LED immediately after door is closed
                if led_id and device_config.fsm_config["led_enabled"]:
                    await self._update_led_state(led_id, None, session_id)
            else:
                # Nepožadujeme čekání na zavření – pokračujeme dál
                if led_id and device_config.fsm_config["led_enabled"]:
                    await self._update_led_state(led_id, None, session_id)
            
            return True
            
        except SectionError as e:
            await error_manager.handle_error(e)
            return False
        except Exception as e:
            logger.error(f"Unexpected error in operator section {section.section_id}: {e}")
            await error_manager.handle_error(
                SectionError(
                    f"Unexpected error: {e}",
                    ErrorType.UNKNOWN,
                    ErrorCode.UNKNOWN,
                    section_id=str(section.section_id)
                )
            )
            return False
    
    async def _handle_customer_section_sequence(
        self,
        session_id: str,
        section: SectionConfig, 
        total_sections: int,
        current_index: int
    ) -> bool:
        """Handle processing of a single section FOR CUSTOMERS - with door closing"""
        
        try:
            section_id = section.section_id  # Visual ID for user interface
            lock_id = section.lock_id        # Hardware lock ID for commands
            is_tempered = section.is_tempered
            led_id = section.led_section
            
            # Unlock the locker FIRST
            await ws_manager.send(session_id, {
                "type": "section_update",
                "section_id": section_id,
                "status": "unlocking",
                "message": f"Unlocking locker {section_id} (hardware lock {lock_id})"
            })
            
            unlock_success = await self.locker_controller.unlock_locker(lock_id, is_tempered)
            if not unlock_success:
                raise SectionError(
                    f"Failed to unlock locker {section_id}",
                    ErrorType.HARDWARE,
                    ErrorCode.UNLOCK_FAILED,
                    section_id=str(section_id)
                )
            
            # ONLY if unlock was successful, set LED to green (current section)
            if led_id and device_config.fsm_config["led_enabled"]:
                await self._update_led_state(led_id, LEDColor.GREEN, session_id)
            
            # Wait for door to be opened
            await ws_manager.send(session_id, {
                "type": "section_update",
                "section_id": section_id,
                "status": "waiting_open",
                "message": f"Waiting for door {section_id} to be opened"
            })
            
            door_opened = await self._wait_for_door_state(
                section_id, lock_id, True, self.door_timeouts["open"], is_tempered, session_id
            )
            
            if not door_opened:
                raise SectionError(
                    f"Timeout waiting for door {section_id} to open",
                    ErrorType.TIMEOUT,
                    ErrorCode.DOOR_TIMEOUT,
                    section_id=str(section_id)
                )
            
            # IMMEDIATELY after door opens, lock tempered locks so door can be closed
            if is_tempered:
                await ws_manager.send(session_id, {
                    "type": "section_update",
                    "section_id": section_id,
                    "status": "locking_after_open",
                    "message": f"Door {section_id} opened, locking mechanism to allow closing"
                })
                
                lock_success = await self.locker_controller.lock_locker(lock_id, is_tempered)
                if not lock_success:
                    raise SectionError(
                        f"Failed to lock locker {section_id} after opening",
                        ErrorType.HARDWARE,
                        ErrorCode.LOCK_FAILED,
                        section_id=str(section_id)
                    )
            
            # Wait for door to be closed
            await ws_manager.send(session_id, {
                "type": "section_update",
                "section_id": section_id,
                "status": "waiting_close",
                "message": f"Door {section_id} opened, waiting for close"
            })
            
            # Use different timeout for tempered vs non-tempered lockers
            close_timeout = (device_config.fsm_config["door_close_timeout"] if is_tempered 
                           else device_config.fsm_config["non_tempered_close_timeout"])
            
            logger.info(f"Using close timeout of {close_timeout}s for {'tempered' if is_tempered else 'non-tempered'} locker {section_id}")
            
            door_closed = await self._wait_for_door_state(
                section_id, lock_id, False, close_timeout, is_tempered, session_id
            )
            
            if not door_closed:
                raise SectionError(
                    f"Timeout waiting for door {section_id} to close",
                    ErrorType.TIMEOUT,
                    ErrorCode.DOOR_TIMEOUT,
                    section_id=str(section_id)
                )
            
            # Turn off LED immediately after door is closed
            if led_id and device_config.fsm_config["led_enabled"]:
                await self._update_led_state(led_id, None, session_id)
            
            return True
            
        except SectionError as e:
            await error_manager.handle_error(e)
            return False
        except Exception as e:
            logger.error(f"Unexpected error in section {section.section_id}: {e}")
            await error_manager.handle_error(
                SectionError(
                    f"Unexpected error: {e}",
                    ErrorType.UNKNOWN,
                    ErrorCode.UNKNOWN,
                    section_id=str(section.section_id)
                )
            )
            return False
    
    async def _wait_for_door_state(
        self, 
        section_id: int,    # Visual ID for logging/messages
        lock_id: int,       # Hardware lock ID for commands
        expected_open: bool, 
        timeout: int,
        is_tempered: bool,
        session_id: str = None  # Add session_id for WebSocket updates
    ) -> bool:
        """Wait for door to reach expected state with timeout, countdown, and UI updates"""
        
        start_time = asyncio.get_event_loop().time()
        consecutive_failures = 0
        max_consecutive_failures = 3  # Allow 3 consecutive failures before giving up
        last_countdown_second = None  # Track last logged second to avoid spam
        
        while True:
            current_time = asyncio.get_event_loop().time()
            elapsed_time = current_time - start_time
            remaining_time = max(0, timeout - elapsed_time)
            
            if elapsed_time > timeout:
                logger.error(f"Timeout waiting for door {section_id} state")
                if session_id:
                    await ws_manager.send(session_id, {
                        "type": "door_timeout",
                        "section_id": section_id,
                        "message": f"Timeout waiting for door {section_id} to {'open' if expected_open else 'close'}"
                    })
                return False
            
            # Log and send countdown every second
            countdown_seconds = int(remaining_time)
            if last_countdown_second != countdown_seconds:
                # Log to console every second
                logger.info(f"Waiting for door {section_id} {'open' if expected_open else 'closed'}, timeout in {countdown_seconds} seconds")
                
                # Send WebSocket update to HTML interface every second
                if session_id:
                    await ws_manager.send(session_id, {
                        "type": "door_countdown",
                        "section_id": section_id,
                        "action": "open" if expected_open else "close",
                        "remaining_seconds": countdown_seconds,
                        "message": f"Waiting for door {section_id} to {'open' if expected_open else 'close'} - {countdown_seconds}s remaining"
                    })
                
                last_countdown_second = countdown_seconds
            
            door_state = await self.locker_controller.check_door_state(lock_id, is_tempered)
            
            if door_state is None:
                consecutive_failures += 1
                logger.warning(f"Could not check door state for {section_id} (failure {consecutive_failures}/{max_consecutive_failures}), timeout in {countdown_seconds} seconds")
                
                # Send WebSocket warning to HTML interface
                if session_id:
                    await ws_manager.send(session_id, {
                        "type": "door_check_warning",
                        "section_id": section_id,
                        "failure_count": consecutive_failures,
                        "max_failures": max_consecutive_failures,
                        "remaining_seconds": countdown_seconds,
                        "message": f"Communication issue with door {section_id} (attempt {consecutive_failures}/{max_consecutive_failures})"
                    })
                
                if consecutive_failures >= max_consecutive_failures:
                    logger.error(f"Max consecutive failures reached for door state check on {section_id}, giving up")
                    if session_id:
                        await ws_manager.send(session_id, {
                            "type": "door_check_failed", 
                            "section_id": section_id,
                            "message": f"Door {section_id} communication failed after {max_consecutive_failures} attempts"
                        })
                    return False
                    
                await asyncio.sleep(self.polling_interval)
                continue
            
            # Reset consecutive failures on successful read
            consecutive_failures = 0
            
            if door_state == expected_open:
                logger.info(f"Door {section_id} reached expected state: {'open' if expected_open else 'closed'}")
                if session_id:
                    await ws_manager.send(session_id, {
                        "type": "door_state_reached",
                        "section_id": section_id,
                        "state": "open" if expected_open else "closed",
                        "message": f"Door {section_id} successfully {'opened' if expected_open else 'closed'}"
                    })
                return True
            
            await asyncio.sleep(self.polling_interval)
    
    async def _initialize_sequence_leds(self, sections: List[SectionConfig], sequence_id: str):
        """Initialize LED system for sequence - set all to yellow with retry logic"""
        if not device_config.fsm_config["led_enabled"]:
            return
        
        max_attempts = 2
        
        # Enable LED system with retries
        enable_success = False
        for attempt in range(max_attempts):
            try:
                enable_success = await self.locker_controller.enable_led_system()
                if enable_success:
                    break
                if attempt < max_attempts - 1:
                    logger.warning(f"LED enable attempt {attempt + 1} failed, retrying...")
                    await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"Exception during LED enable attempt {attempt + 1}: {e}")
                if attempt < max_attempts - 1:
                    await asyncio.sleep(1)
        
        if not enable_success:
            logger.warning("Failed to enable LED system after 2 attempts, continuing without LEDs")
            return
        
        # Clear any previous LED state
        self.led_states.clear()
        
        # Set all section LEDs to yellow
        for section in sections:
            if section.led_section:
                self.led_states[section.led_section] = LEDColor.YELLOW
        
        if self.led_states:
            # Try to set initial LED colors with retries
            colors_success = False
            for attempt in range(max_attempts):
                try:
                    colors_success = await self.locker_controller.set_multiple_led_colors(self.led_states)
                    if colors_success:
                        break
                    if attempt < max_attempts - 1:
                        logger.warning(f"LED colors setup attempt {attempt + 1} failed, retrying...")
                        await asyncio.sleep(1)
                except Exception as e:
                    logger.error(f"Exception during LED colors setup attempt {attempt + 1}: {e}")
                    if attempt < max_attempts - 1:
                        await asyncio.sleep(1)
            
            if colors_success:
                await ws_manager.send(sequence_id, {
                    "type": "led_initialized",
                    "led_colors": dict(self.led_states),
                    "message": "LED system initialized - all sections set to yellow"
                })
            else:
                logger.warning("Failed to set initial LED colors after 2 attempts, continuing sequence")
                await ws_manager.send(sequence_id, {
                    "type": "led_warning",
                    "message": "Warning: LED initialization failed, continuing sequence without LED indicators"
                })
    
    async def _disable_sequence_leds(self, sequence_id: str):
        """Disable LED system at end of sequence with retry logic"""
        if not device_config.fsm_config["led_enabled"]:
            return
        
        max_attempts = 2
        
        # Clear LED state
        self.led_states.clear()
        
        # Try to disable LED system with retries
        success = False
        for attempt in range(max_attempts):
            try:
                success = await self.locker_controller.disable_led_system()
                if success:
                    break
                if attempt < max_attempts - 1:
                    logger.warning(f"LED disable attempt {attempt + 1} failed, retrying...")
                    await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"Exception during LED disable attempt {attempt + 1}: {e}")
                if attempt < max_attempts - 1:
                    await asyncio.sleep(1)
        
        if success:
            await ws_manager.send(sequence_id, {
                "type": "led_disabled",
                "message": "LED system disabled"
            })
        else:
            logger.warning("Failed to disable LED system after 2 attempts")
            await ws_manager.send(sequence_id, {
                "type": "led_warning",
                "message": "Warning: LED system disable failed, but sequence completed"
            })
    
    async def stop_sequence(self, session_id: str) -> bool:
        """Stop an active sequence"""
        task = self.active_sequences.get(session_id)
        if task and not task.done():
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
            
            self.active_sequences.pop(session_id, None)
            session_manager.update_session(session_id, status=SessionStatus.FAILED)
            
            logger.info(f"Stopped sequence {session_id}")
            return True
        
        return False
    
    def get_active_sequences(self) -> List[str]:
        """Get list of active sequence IDs"""
        return list(self.active_sequences.keys())

# Global sequence manager instance
sequence_manager = SequenceManager() 