from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

class StorageCategory(BaseModel):
    """
    Represents a storage category.
    """
    id: int
    size_category: int
    price: int
    created_at: datetime
    updated_at: datetime

class StorageReservation(BaseModel):
    """
    Represents a storage reservation.
    """
    id: int
    uuid: str
    box_uuid: Optional[str] = None
    section_id: Optional[str] = None
    status: int
    reservation_pin: Optional[str] = None
    email: Optional[str] = None
    price: float
    max_days: Optional[int] = None
    paid_status: Optional[str] = None
    category: Optional[int] = None
    last_update: datetime
    created_at: datetime

class CreateReservationRequest(BaseModel):
    """
    Request model for creating a storage reservation.
    """
    size_category: int = Field(..., description="Size category of the box")
    email: str = Field(..., description="Customer's email address")

class CreateReservationResponse(BaseModel):
    """
    Response model for creating a storage reservation.
    """
    success: bool
    message: str
    reservation: Optional[StorageReservation] = None
    session_id: Optional[str] = None
    websocket_url: Optional[str] = None

class PickupRequest(BaseModel):
    """
    Request model for picking up a stored item.
    """
    reservation_pin: str = Field(..., description="Reservation PIN for pickup")

class PickupResponse(BaseModel):
    """
    Response model for picking up a stored item.
    """
    success: bool
    message: str
    surcharge: Optional[float] = None
    session_id: Optional[str] = None
    websocket_url: Optional[str] = None

class SurchargeRequest(BaseModel):
    """
    Request model for paying a surcharge.
    """
    reservation_pin: str = Field(..., description="Reservation PIN for surcharge payment")

class SurchargeResponse(BaseModel):
    """
    Response model for paying a surcharge.
    """
    success: bool
    message: str
    session_id: Optional[str] = None
    websocket_url: Optional[str] = None

class ListCategoriesResponse(BaseModel):
    """
    Response model for listing storage categories.
    """
    success: bool
    categories: List[StorageCategory]
