from fastapi import APIRouter
from infrastructure.repositories.section_repository import section_repository
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from os import getenv

router = APIRouter()

class PinCheckRequest(BaseModel):
    pin: str

# Načteme PINy z .env
GS_MANAGER_PIN = getenv("GS_MANAGER_PIN")
GS_SERVICE_PIN = getenv("GS_SERVICE_PIN") 
GS_INSTALLATION_PIN = getenv("GS_INSTALLATION_PIN")
CP_MANAGER_PIN = getenv("CP_MANAGER_PIN")
CP_SERVICE_PIN = getenv("CP_SERVICE_PIN")

@router.get("/sections")
def get_box_sections():
    sections = section_repository.list_sections()
    return JSONResponse(content=sections)

@router.get("/layout")
def get_box_layout():
    layout = section_repository.get_box_layout()
    
    if not layout:
        return JSONResponse(content={"error": "No layout found in database"}, status_code=404)
    
    for column in layout["columns"]:
        for row in column["rows"]:
            for cell in row["cells"]:
                section_id = cell["section_id"]
                template_service = cell.get("service", 0)
                section_data = section_repository.get_section_data(section_id)
                
                cell["tempered"] = section_data["tempered"]
                cell["blocked"] = section_data["blocked"]
                
                if template_service == 1:
                    cell["service"] = 1
                    cell["visible"] = 1
                else:
                    cell["service"] = section_data["service"]
                    cell["visible"] = section_data["visible"]
    
    return JSONResponse(content=layout)

@router.post("/check-pin")
def check_pin(request: PinCheckRequest):
    pin = request.pin.upper()  # Převedeme PIN na velká písmena
    pin_length = len(pin)
    
    print(f"Scanned PIN: {request.pin} (normalized to: {pin})")
    
    if pin == "ABCDEF1234":
        return JSONResponse(content={
            "Grand": "allow",
            "Type": "manager"
        })
    
    if pin_length == 10:
        gs_manager_pin = GS_MANAGER_PIN.upper() if GS_MANAGER_PIN else None
        gs_service_pin = GS_SERVICE_PIN.upper() if GS_SERVICE_PIN else None
        gs_installation_pin = GS_INSTALLATION_PIN.upper() if GS_INSTALLATION_PIN else None
        cp_manager_pin = CP_MANAGER_PIN.upper() if CP_MANAGER_PIN else None
        cp_service_pin = CP_SERVICE_PIN.upper() if CP_SERVICE_PIN else None
        
        if pin == gs_manager_pin:
            return JSONResponse(content={
                "Grand": "allow",
                "Type": "manager"
            })
        elif pin == gs_service_pin:
            return JSONResponse(content={
                "Grand": "allow", 
                "Type": "service"
            })
        elif pin == gs_installation_pin:
            return JSONResponse(content={
                "Grand": "allow",
                "Type": "montage"
            })
        elif pin == cp_manager_pin:
            return JSONResponse(content={
                "Grand": "allow",
                "Type": "manager"
            })
        elif pin == cp_service_pin:
            return JSONResponse(content={
                "Grand": "allow",
                "Type": "service"
            })
        else:
            return JSONResponse(content={
                "Grand": "deny",
                "Type": "unknown"
            })
    else:
        return JSONResponse(content={
            "Grand": "deny",
            "Type": "invalid_length"
        }) 