# Waffles Backend

> [!CAUTION]
> Do not use in production yet!

### Implemetation progress

- [ ] Orders module
- [X] Sale module
- [ ] Storage module

### Access modes
- [ ] Installation mode
- [ ] Service mode
- [ ] Manager mode

## Architecture Overview

The system follows Domain-Driven Design principles with clean separation between business logic and infrastructure concerns.

```
waffles-backend/
├── domains/                      # Business logic layer
│   └── product/                  # Product domain operations
│       ├── flow_coordinator.py   # Product flow orchestration
│       ├── flow_engine.py        # Flow execution engine
│       ├── step_handlers.py      # Individual step implementations
│       ├── service.py            # Product business logic
│       └── websocket_handler.py  # WebSocket communication
├── infrastructure/               # Infrastructure layer
│   ├── repositories/             # Database abstraction layer
│   └── external_apis/            # External API client implementations
├── managers/                     # System coordination layer
│   ├── session_manager.py        # Session lifecycle management
│   ├── ws_manager.py             # WebSocket connection management
│   ├── websocket_handlers.py     # WebSocket message handlers
│   ├── sequence_manager.py       # Hardware sequence orchestration
│   ├── error_manager.py          # Error handling and logging
│   └── transaction_handler.py    # Transaction processing
├── hardware/                     # Hardware abstraction layer
│   ├── locker_control.py         # Locker operations and control
│   ├── electronics_api.py        # Low-level hardware communication
│   └── boardctl.py               # Board control interface
├── sale/                         # Sales operations module
│   ├── models.py                 # Sales data models
│   └── router.py                 # Sales API endpoints
├── transaction/                  # Transaction management module
│   ├── models.py                 # Transaction data models
│   ├── manager.py                # Transaction business logic
│   └── router.py                 # Transaction API endpoints
├── auth/                         # Authentication module
│   └── auth_bearer.py            # Bearer token authentication
├── storage/                      # Storage operations
│   └── router.py                 # Storage API endpoints
├── configs/                      # Configuration files
├── migrations/                   # Database migrations
├── orders/                       # Order management
├── config.py                     # Application configuration
└── main.py                       # Application entry point
```

## Core Components

### Domains Layer
The `domains/` directory contains business logic organized by domain boundaries. Each domain encapsulates related business operations and maintains clear boundaries.

**Product Domain** (`domains/product/`):
- `flow_coordinator.py`: Orchestrates product-related workflows
- `flow_engine.py`: Executes flow steps and manages state transitions
- `step_handlers.py`: Implements individual workflow steps
- `service.py`: Product business logic and operations
- `websocket_handler.py`: Handles real-time communication for product operations

### Infrastructure Layer
The `infrastructure/` directory provides abstractions for external concerns:

**Repositories** (`infrastructure/repositories/`):
- Database access abstraction
- Implements repository pattern for data persistence
- Provides clean interfaces for domain operations

**External APIs** (`infrastructure/external_apis/`):
- External service integration
- API client implementations
- Request/response handling

### Managers Layer
The `managers/` directory contains system coordination components:

- `session_manager.py`: Manages hardware session lifecycle
- `ws_manager.py`: Handles WebSocket connections and message routing
- `websocket_handlers.py`: WebSocket message processing and routing
- `sequence_manager.py`: Orchestrates hardware control sequences
- `error_manager.py`: Centralized error handling and logging
- `transaction_handler.py`: Processes payment transactions

### Hardware Layer
The `hardware/` directory provides hardware abstraction:

- `locker_control.py`: High-level locker operations
- `electronics_api.py`: Low-level hardware communication protocols
- `boardctl.py`: Board control interface implementation

### Transaction Endpoints
- `POST /transaction/callback`: Payment callback processing

### System Endpoints
- `GET /health`: System health check
- `WebSocket /ws/{session_id}`: Real-time communication

## WebSocket Communication

The system uses WebSocket connections for real-time hardware control and status updates. Each hardware session establishes a WebSocket connection for bidirectional communication between the backend and frontend.

### Message Types

**Frontend → Backend (requires response):**
- `ping`: Connection keep-alive → responds with `pong`
- `*_screen_ready`: Screen ready signals (e.g., `payment_screen_ready`) → responds with status updates
- `ready_for_insert`/`ready_for_pickup`: Ready for hardware operation → responds with `opening_locker`
- `ready_for_sequence`: Ready for FSM sequence → responds with `sequence_starting`

## Configuration

The application uses environment-based configuration through `config.py`. Key configuration areas include:

- Database connection settings
- API authentication tokens
- Hardware configuration paths
- External service endpoints

## Dependencies

Core dependencies include:
- FastAPI: Web framework
- SQLAlchemy: Database ORM
- WebSockets: Real-time communication
- Pydantic: Data validation
- MySQL Connector: Database driver 