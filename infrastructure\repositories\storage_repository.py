import mysql.connector
from os import getenv
from typing import List, Dict, Optional, Any
import logging
import random
import string
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class StorageRepository:
    def __init__(self):
        self.db_config = {
            "host": getenv("DB_HOST"),
            "port": int(getenv("DB_PORT")),
            "user": getenv("DB_USER"),
            "password": getenv("DB_PASSWORD"),
            "database": getenv("DB_NAME"),
        }

    def _get_connection(self):
        return mysql.connector.connect(**self.db_config)

    async def get_storage_categories(self) -> List[Dict[str, Any]]:
        """
        Retrieves all storage categories from the database.
        """
        conn = self._get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            cursor.execute("SELECT id, size_category, price, created_at, updated_at FROM storage_categories")
            return cursor.fetchall()
        except mysql.connector.Error as err:
            logger.error(f"Database error: {err}")
            return []
        finally:
            cursor.close()
            conn.close()

    async def find_available_box(self, size_category: int) -> Optional[Dict[str, Any]]:
        """
        Finds an available box of a given size category.
        This is a placeholder logic. In a real scenario, you would have a table of boxes
        and their statuses. Here, we'll just simulate finding a box.
        """
        # In a real implementation, you would query a 'boxes' table.
        # For now, we'll assume a box is always available and return a dummy box.
        return {"box_uuid": f"box_{size_category}_{''.join(random.choices(string.ascii_uppercase + string.digits, k=6))}", "section_id": random.randint(1, 100)}

    async def create_reservation(self, email: str, size_category: int, price: float, box: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Creates a new storage reservation in the database.
        """
        conn = self._get_connection()
        cursor = conn.cursor(dictionary=True)
        reservation_pin = ''.join(random.choices(string.digits, k=6))
        reservation = None
        try:
            cursor.execute(
                """
                INSERT INTO storage_reservations (email, category, price, box_uuid, section_id, status, reservation_pin, paid_status)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """,
                (email, size_category, price, box["box_uuid"], box["section_id"], 1, reservation_pin, "unpaid"),
            )
            conn.commit()
            reservation_id = cursor.lastrowid
            cursor.execute("SELECT * FROM storage_reservations WHERE id = %s", (reservation_id,))
            reservation = cursor.fetchone()
        except mysql.connector.Error as err:
            logger.error(f"Database error: {err}")
            conn.rollback()
        finally:
            cursor.close()
            conn.close()
        return reservation

    async def find_reservation_by_pin(self, pin: str) -> Optional[Dict[str, Any]]:
        """
        Finds an active reservation by its PIN.
        """
        conn = self._get_connection()
        cursor = conn.cursor(dictionary=True)
        try:
            cursor.execute("SELECT * FROM storage_reservations WHERE reservation_pin = %s AND status = 1", (pin,))
            return cursor.fetchone()
        except mysql.connector.Error as err:
            logger.error(f"Database error: {err}")
            return None
        finally:
            cursor.close()
            conn.close()

    async def calculate_surcharge(self, reservation: Dict[str, Any]) -> float:
        """
        Calculates the surcharge for a late pickup.
        """
        created_at = reservation["created_at"]
        if isinstance(created_at, str):
            created_at = datetime.fromisoformat(created_at)

        if (datetime.now() - created_at) > timedelta(days=1):
            # This is a placeholder for the actual surcharge logic.
            # You might want to have a more complex calculation based on the number of extra days.
            return 5.0  # 5 EUR surcharge
        return 0.0

    async def update_reservation_status(self, reservation_id: int, status: int, paid_status: Optional[str] = None) -> bool:
        """
        Updates the status of a reservation.
        """
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            if paid_status:
                cursor.execute("UPDATE storage_reservations SET status = %s, paid_status = %s, last_update = NOW() WHERE id = %s", (status, paid_status, reservation_id))
            else:
                cursor.execute("UPDATE storage_reservations SET status = %s, last_update = NOW() WHERE id = %s", (status, reservation_id))
            conn.commit()
            return cursor.rowcount > 0
        except mysql.connector.Error as err:
            logger.error(f"Database error: {err}")
            conn.rollback()
            return False
        finally:
            cursor.close()
            conn.close()

storage_repository = StorageRepository()
